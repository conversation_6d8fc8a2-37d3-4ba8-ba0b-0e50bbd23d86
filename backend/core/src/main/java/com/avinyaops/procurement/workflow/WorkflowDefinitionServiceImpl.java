package com.avinyaops.procurement.workflow;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.avinyaops.procurement.organization.department.DepartmentService;
import com.avinyaops.procurement.user.UserService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@AllArgsConstructor
@Transactional(readOnly = true)
@Slf4j
public class WorkflowDefinitionServiceImpl implements WorkflowDefinitionService {

    private final WorkflowDefinitionRepository workflowDefinitionRepository;
    private final RecordApprovalRepository recordApprovalRepository;
    private final ApproverTimelineRepository approverTimelineRepository;
    private final UserService userService;
    private final DepartmentService departmentService;
    private final OrganizationFormsRepository organizationFormsRepository;
    
    @Override
    public List<WorkflowDefinition> getAllByOrganizationId(long organizationId) {
        return workflowDefinitionRepository.findAllByOrganizationId(organizationId);
    }
    
    @Override
    public WorkflowDefinition get(long organizationId, long id) {
        return workflowDefinitionRepository.findOneByOrganizationIdAndId(organizationId, id);
    }
    
    @Override
    @Transactional
    public WorkflowDefinition save(WorkflowDefinition workflowDefinition) {
        return workflowDefinitionRepository.save(workflowDefinition);
    }
    
    @Override
    public boolean checkInProgressApproval(long organizationId, long formId, long recordId) {
        Optional<RecordApproval> optional = recordApprovalRepository.findOneByOrganization_IdAndOrganizationForms_IdAndRecordId(organizationId, formId, recordId);
        return optional.isPresent();
    }
}
