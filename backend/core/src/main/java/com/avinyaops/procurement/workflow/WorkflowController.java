package com.avinyaops.procurement.workflow;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.AllArgsConstructor;

@RestController
@RequestMapping("/api/v1/organizations/{organizationId}/workflows")
@AllArgsConstructor
public class WorkflowController {
    
    private final WorkflowDefinitionService workflowDefinitionService;
    private final WorkflowService workflowService;
    
    // Workflow Definition endpoints
    
    @GetMapping("/definitions")
    public ResponseEntity<List<WorkflowDefinitionResponse>> getAllWorkflowDefinitions(
            @PathVariable Long organizationId) {
        List<WorkflowDefinition> definitions = workflowDefinitionService.getAllByOrganizationId(organizationId);
        List<WorkflowDefinitionResponse> responses = definitions.stream()
                .map(this::toWorkflowDefinitionResponse)
                .toList();
        
        return ResponseEntity.ok(responses);
    }
    
    @GetMapping("/definitions/{definitionId}")
    public ResponseEntity<WorkflowDefinitionResponse> getWorkflowDefinition(
            @PathVariable Long organizationId,
            @PathVariable Long definitionId) {
        WorkflowDefinition definition = workflowDefinitionService.get(organizationId, definitionId);
        return ResponseEntity.ok(toWorkflowDefinitionResponse(definition));
    }
    
    @PostMapping("/definitions")
    public ResponseEntity<WorkflowDefinitionResponse> createWorkflowDefinition(
            @PathVariable Long organizationId,
            @RequestBody WorkflowDefinitionRequest request) {
        // Convert request to entity
        WorkflowDefinition definition = toWorkflowDefinitionEntity(request);
        definition.setOrganizationId(organizationId);
        
        // Save the definition
        WorkflowDefinition saved = workflowDefinitionService.save(definition);
        
        return ResponseEntity.ok(toWorkflowDefinitionResponse(saved));
    }
    
    // Workflow Approval endpoints
    
    @PostMapping("/approvals/initiate")
    public ResponseEntity<RecordApprovalResponse> initiateWorkflow(
            @PathVariable Long organizationId,
            @RequestBody InitiateWorkflowRequest request) {
        RecordApproval recordApproval = workflowService.initiateWorkflow(
                organizationId, 
                request.getFormId(), 
                request.getRecordId(), 
                request.getInitiatorUserId());
        
        return ResponseEntity.ok(toRecordApprovalResponse(recordApproval));
    }
    
    @PostMapping("/approvals/{recordApprovalId}/approve")
    public ResponseEntity<RecordApprovalResponse> approveRecord(
            @PathVariable Long organizationId,
            @PathVariable Long recordApprovalId,
            @RequestBody ApprovalActionRequest request) {
        RecordApproval recordApproval = workflowService.approveRecord(
                recordApprovalId, 
                request.getUserId(), 
                request.getComment());
        
        return ResponseEntity.ok(toRecordApprovalResponse(recordApproval));
    }
    
    @PostMapping("/approvals/{recordApprovalId}/reject")
    public ResponseEntity<RecordApprovalResponse> rejectRecord(
            @PathVariable Long organizationId,
            @PathVariable Long recordApprovalId,
            @RequestBody ApprovalActionRequest request) {
        RecordApproval recordApproval = workflowService.rejectRecord(
                recordApprovalId, 
                request.getUserId(), 
                request.getComment());
        
        return ResponseEntity.ok(toRecordApprovalResponse(recordApproval));
    }
    
    @GetMapping("/approvals/pending/user/{userId}")
    public ResponseEntity<List<RecordApprovalResponse>> getPendingApprovals(
            @PathVariable Long organizationId,
            @PathVariable Long userId) {
        List<RecordApproval> approvals = workflowService.getPendingApprovals(organizationId, userId);
        List<RecordApprovalResponse> responses = approvals.stream()
                .map(this::toRecordApprovalResponse)
                .toList();
        
        return ResponseEntity.ok(responses);
    }
    
    @GetMapping("/approvals/initiated/user/{userId}")
    public ResponseEntity<List<RecordApprovalResponse>> getInitiatedApprovals(
            @PathVariable Long organizationId,
            @PathVariable Long userId) {
        List<RecordApproval> approvals = workflowService.getInitiatedApprovals(organizationId, userId);
        List<RecordApprovalResponse> responses = approvals.stream()
                .map(this::toRecordApprovalResponse)
                .toList();
        
        return ResponseEntity.ok(responses);
    }
    
    @GetMapping("/approvals/{recordApprovalId}/timeline")
    public ResponseEntity<List<ApprovalTimelineResponse>> getApprovalTimeline(
            @PathVariable Long organizationId,
            @PathVariable Long recordApprovalId) {
        List<ApproverTimeline> timeline = workflowService.getApprovalTimeline(recordApprovalId);
        List<ApprovalTimelineResponse> responses = timeline.stream()
                .map(this::toApprovalTimelineResponse)
                .toList();
        
        return ResponseEntity.ok(responses);
    }
    
    // Helper methods for entity-DTO conversion
    
    private WorkflowDefinitionResponse toWorkflowDefinitionResponse(WorkflowDefinition definition) {
        if (definition == null) {
            return null;
        }
        
        List<WorkflowCriteriaDto> criteriaDtos = null;
        if (definition.getCriteriaConfig() != null && definition.getCriteriaConfig().getWorkflowCriteriaList() != null) {
            criteriaDtos = definition.getCriteriaConfig().getWorkflowCriteriaList().stream()
                    .map(this::toWorkflowCriteriaDto)
                    .collect(Collectors.toList());
        }
        
        List<ApproverConfigDto> approverDtos = null;

        if (definition.getApprovals() != null) {
            approverDtos = definition.getApprovals().stream()
                    .map(this::toApproverConfigDto)
                    .collect(Collectors.toList());
        }
        
        return WorkflowDefinitionResponse.builder()
                .id(definition.getId())
                .organizationId(definition.getOrganizationId())
                .formId(definition.getFormId())
                .name(definition.getName())
                .description(definition.getDescription())
                .isActive(true) // Assuming default is active
                .criteria(criteriaDtos)
                .approvers(approverDtos)
                .createdAt(definition.getCreatedDate() != null ? 
                        LocalDateTime.ofInstant(definition.getCreatedDate(), ZoneId.systemDefault()) : null)
                .updatedAt(definition.getLastModifiedDate() != null ? 
                        LocalDateTime.ofInstant(definition.getLastModifiedDate(), ZoneId.systemDefault()) : null)
                .build();
    }
    
    private WorkflowDefinition toWorkflowDefinitionEntity(WorkflowDefinitionRequest request) {
        if (request == null) {
            return null;
        }
        
        List<WorkflowCriteria> criteria = null;
        if (request.getCriteria() != null) {
            criteria = request.getCriteria().stream()
                    .map(this::toWorkflowCriteriaEntity)
                    .collect(Collectors.toList());
        }
        
        List<Approvals> approvers = null;
        if (request.getApprovers() != null) {
            approvers = request.getApprovers().stream()
                    .map(this::toApprovalsEntity)
                    .collect(Collectors.toList());
        }
        
        WorkflowDefinition definition = new WorkflowDefinition();
        definition.setName(request.getName());
        definition.setDescription(request.getDescription());
        definition.setFormId(request.getFormId());
        
        // Set criteria
        if (criteria != null) {
            CriteriaConfig criteriaConfig = new CriteriaConfig();
            criteriaConfig.setWorkflowCriteriaList(criteria);
            definition.setCriteriaConfig(criteriaConfig);
        }
        
        // Set approvers
        definition.setApprovals(approvers != null ? approvers : new ArrayList<>());
        
        return definition;
    }
    
    private WorkflowCriteriaDto toWorkflowCriteriaDto(WorkflowCriteria criteria) {
        if (criteria == null) {
            return null;
        }
        
        return WorkflowCriteriaDto.builder()
                .id(criteria.getId())
                .conjunctiveCondition(criteria.getConjunctiveCondition())
                .fieldName(criteria.getField())
                .operation(criteria.getOperation())
                .value(criteria.getRuleValue())
                .values(criteria.getRuleValues())
                .build();
    }
    
    private WorkflowCriteria toWorkflowCriteriaEntity(WorkflowCriteriaDto dto) {
        if (dto == null) {
            return null;
        }
        
        WorkflowCriteria criteria = new WorkflowCriteria();
        criteria.setId(dto.getId());
        criteria.setConjunctiveCondition(dto.getConjunctiveCondition());
        criteria.setField(dto.getFieldName());
        criteria.setOperation(dto.getOperation());
        criteria.setRuleValue(dto.getValue());
        criteria.setRuleValues(dto.getValues());
        
        return criteria;
    }
    
    private ApproverConfigDto toApproverConfigDto(Approvals approvals) {
        if (approvals == null) {
            return null;
        }
        
        return ApproverConfigDto.builder()
                .id(null) // Approvals doesn't have an ID field
                .executionOrder(0) // Approvals doesn't have an executionOrder field
                .approverType(approvals.getApproverType())
                .approverId(approvals.getApproverId())
                .autoApproval(null) // Approvals doesn't have an autoApproval field
                .timeoutDays(null) // Approvals doesn't have a timeoutDays field
                .build();
    }
    
    private Approvals toApprovalsEntity(ApproverConfigDto dto) {
        if (dto == null) {
            return null;
        }
        
        Approvals approvals = new Approvals();
        approvals.setApproverType(dto.getApproverType());
        approvals.setApproverId(dto.getApproverId());
        
        return approvals;
    }
    
    private RecordApprovalResponse toRecordApprovalResponse(RecordApproval recordApproval) {
        if (recordApproval == null) {
            return null;
        }
        
        return RecordApprovalResponse.builder()
                .id(recordApproval.getId())
                .organizationId(recordApproval.getOrganization() != null ? recordApproval.getOrganization().getId() : null)
                .formId(recordApproval.getOrganizationForms() != null ? recordApproval.getOrganizationForms().getId() : null)
                .recordId(recordApproval.getRecordId())
                .workflowDefinitionId(null) // RecordApproval doesn't have a workflowDefinitionId field
                .initiatorUserId(recordApproval.getWorkflowInitiatorUser() != null ? recordApproval.getWorkflowInitiatorUser().getId() : null)
                .initiatorName(recordApproval.getWorkflowInitiatorUser() != null ? recordApproval.getWorkflowInitiatorUser().getUsername() : null)
                .status(recordApproval.getApprovalStatus())
                .formName(recordApproval.getOrganizationForms() != null ? recordApproval.getOrganizationForms().getName() : null)
                .recordName(null) // RecordApproval doesn't have a recordName field
                .createdAt(recordApproval.getCreatedDate() != null ? 
                        LocalDateTime.ofInstant(recordApproval.getCreatedDate(), ZoneId.systemDefault()) : null)
                .updatedAt(recordApproval.getLastModifiedDate() != null ? 
                        LocalDateTime.ofInstant(recordApproval.getLastModifiedDate(), ZoneId.systemDefault()) : null)
                .completedAt(recordApproval.getLastActionDate() != null ? 
                        LocalDateTime.ofInstant(recordApproval.getLastActionDate(), ZoneId.systemDefault()) : null)
                .currentExecutionOrder(0) // RecordApproval doesn't have a currentExecutionOrder field
                .currentApproverName(recordApproval.getApprovalPendingWithUser() != null ? recordApproval.getApprovalPendingWithUser().getUsername() : null)
                .build();
    }
    
    private ApprovalTimelineResponse toApprovalTimelineResponse(ApproverTimeline timeline) {
        if (timeline == null) {
            return null;
        }
        
        return ApprovalTimelineResponse.builder()
                .id(timeline.getId())
                .executionOrder(timeline.getExecutionOrder())
                .approverType(null) // ApproverTimeline doesn't have an approverType field
                .approverId(timeline.getUserId() != null ? timeline.getUserId().getId() : null)
                .approverName(timeline.getUserId() != null ? timeline.getUserId().getUsername() : null)
                .status(timeline.getApprovalStatus())
                .comment(timeline.getComment())
                .actionDate(timeline.getStatusDate() != null ? 
                        LocalDateTime.ofInstant(timeline.getStatusDate(), ZoneId.systemDefault()) : null)
                .isCurrent(false) // ApproverTimeline doesn't have an isCurrent field
                .build();
    }
}