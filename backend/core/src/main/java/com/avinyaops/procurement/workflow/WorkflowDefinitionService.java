package com.avinyaops.procurement.workflow;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface WorkflowDefinitionService {
    
    /**
     * Gets all workflow definitions for an organization
     * 
     * @param organizationId the organization ID
     * @return list of workflow definitions
     */
    List<WorkflowDefinition> getAllByOrganizationId(long organizationId);
    
    /**
     * Gets a specific workflow definition
     * 
     * @param organizationId the organization ID
     * @param id the workflow definition ID
     * @return the workflow definition
     */
    WorkflowDefinition get(long organizationId, long id);
    
    /**
     * Saves a workflow definition
     * 
     * @param workflowDefinition the workflow definition to save
     * @return the saved workflow definition
     */
    WorkflowDefinition save(WorkflowDefinition workflowDefinition);
    
    /**
     * Checks if there is an in-progress approval for a record
     * 
     * @param organizationId the organization ID
     * @param formId the form ID
     * @param recordId the record ID
     * @return true if there is an in-progress approval, false otherwise
     */
    boolean checkInProgressApproval(long organizationId, long formId, long recordId);
}