/**
 * Service for handling approval workflow-related API operations
 * Integrated with backend API endpoints
 */

import { apiClient } from './client';
import {
  ApprovalWorkflow,
  CreateApprovalWorkflowRequest,
  UpdateApprovalWorkflowRequest,
  FormTypeOption
} from '@/types/approvalWorkflow.types';
import { ApiResponse, PaginatedResponse } from '@/types/api/common';

// Default organization ID for testing/development
const DEFAULT_ORGANIZATION_ID = 40928446087168;

/**
 * Backend DTOs that match the backend structure
 */
interface WorkflowDefinitionResponse {
  id: number;
  organizationId: number;
  formId: number;
  name: string;
  description: string;
  isActive: boolean;
  criteria: WorkflowCriteriaDto[];
  approvers: ApproverConfigDto[];
  createdAt: string;
  updatedAt: string;
}

interface WorkflowCriteriaDto {
  id: number;
  conjunctiveCondition: string;
  fieldName: string;
  operation: 'IS' | 'IS_NOT';
  value: string;
  values: number[];
}

interface ApproverConfigDto {
  id: number;
  executionOrder: number;
  approverType: string;
  approverId: number;
  autoApproval: string;
  timeoutDays: number;
}

interface OrganizationFormsResponse {
  id: number;
  organizationId: number;
  name: string;
  description: string;
  formSchema: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface WorkflowDefinitionRequest {
  id?: number;
  formId: number;
  name: string;
  description: string;
  isActive: boolean;
  criteria: WorkflowCriteriaDto[];
  approvers: ApproverConfigDto[];
}

/**
 * Service class for approval workflow management operations
 */
export class ApprovalWorkflowService {
  private static BASE_URL = '/v1/organizations';

  /**
   * Transform frontend WorkflowCriteria to backend WorkflowCriteriaDto
   */
  private static transformCriteriaToDto(criteria: any): WorkflowCriteriaDto {
    return {
      id: criteria.id ? parseInt(criteria.id) : null,
      conjunctiveCondition: criteria.logicConnector || 'AND',
      fieldName: criteria.fieldType,
      operation: criteria.operator,
      value: criteria.value,
      values: []
    };
  }

  /**
   * Transform backend WorkflowCriteriaDto to frontend WorkflowCriteria
   */
  private static transformCriteriaFromDto(dto: WorkflowCriteriaDto): any {
    return {
      id: dto.id?.toString() || '',
      fieldType: dto.fieldName,
      operator: dto.operation,
      value: dto.value,
      logicConnector: dto.conjunctiveCondition
    };
  }

  /**
   * Transform frontend request to backend request
   */
  private static transformCreateRequestToBackend(
    request: CreateApprovalWorkflowRequest,
    formId: number
  ): WorkflowDefinitionRequest {
    return {
      formId,
      name: request.name,
      description: `Workflow for ${request.formType}`,
      isActive: true,
      criteria: request.criteria.map(this.transformCriteriaToDto),
      approvers: [] // For now, we'll handle approval levels differently
    };
  }

  /**
   * Transform backend response to frontend ApprovalWorkflow
   */
  private static transformResponseToFrontend(response: WorkflowDefinitionResponse): ApprovalWorkflow {
    return {
      id: response.id.toString(),
      name: response.name,
      formType: response.formId.toString(), // We'll need to map this to form name
      criteria: response.criteria?.map(this.transformCriteriaFromDto) || [],
      approvalLevels: response.approvers?.length || 1,
      autoApprove: false, // We'll need to determine this from approvers
      autoReject: false, // We'll need to determine this from approvers
      isActive: response.isActive,
      createdDate: response.createdAt,
      lastModifiedDate: response.updatedAt
    };
  }

  /**
   * Get all approval workflows for an organization
   */
  static async getApprovalWorkflows(organizationId: number = DEFAULT_ORGANIZATION_ID): Promise<PaginatedResponse<ApprovalWorkflow>> {
    try {
      const responses = await apiClient.get<WorkflowDefinitionResponse[]>(
        `${this.BASE_URL}/${organizationId}/workflows/definitions`
      );

      const workflows = responses.map(this.transformResponseToFrontend);

      return {
        data: workflows,
        total: workflows.length,
        page: 1,
        pageSize: workflows.length
      };
    } catch (error) {
      console.error('Error fetching approval workflows:', error);
      throw error;
    }
  }

  /**
   * Get a single approval workflow by ID
   */
  static async getApprovalWorkflow(
    id: string,
    organizationId: number = DEFAULT_ORGANIZATION_ID
  ): Promise<ApprovalWorkflow> {
    try {
      const response = await apiClient.get<WorkflowDefinitionResponse>(
        `${this.BASE_URL}/${organizationId}/workflows/definitions/${id}`
      );

      return this.transformResponseToFrontend(response);
    } catch (error) {
      console.error(`Error fetching approval workflow with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new approval workflow
   */
  static async createApprovalWorkflow(
    data: CreateApprovalWorkflowRequest,
    organizationId: number = DEFAULT_ORGANIZATION_ID
  ): Promise<ApiResponse<ApprovalWorkflow>> {
    try {
      // For now, use a default form ID (we'll need to map form types to IDs)
      const formId = 1; // This should be mapped from data.formType
      
      const requestData = this.transformCreateRequestToBackend(data, formId);
      const response = await apiClient.post<WorkflowDefinitionResponse>(
        `${this.BASE_URL}/${organizationId}/workflows/definitions`,
        requestData
      );

      const workflow = this.transformResponseToFrontend(response);

      return {
        data: workflow,
        message: 'Approval workflow created successfully',
        status: 201
      };
    } catch (error) {
      console.error('Error creating approval workflow:', error);
      throw error;
    }
  }

  /**
   * Update an existing approval workflow
   */
  static async updateApprovalWorkflow(
    id: string,
    data: UpdateApprovalWorkflowRequest,
    organizationId: number = DEFAULT_ORGANIZATION_ID
  ): Promise<ApiResponse<ApprovalWorkflow>> {
    try {
      // Get existing workflow to merge with updates
      const existing = await this.getApprovalWorkflow(id, organizationId);
      
      // For now, use a default form ID (we'll need to map form types to IDs)
      const formId = 1; // This should be mapped from data.formType or existing
      
      const requestData: WorkflowDefinitionRequest = {
        id: parseInt(id),
        formId,
        name: data.name || existing.name,
        description: data.formType ? `Workflow for ${data.formType}` : `Workflow for ${existing.formType}`,
        isActive: data.isActive !== undefined ? data.isActive : existing.isActive,
        criteria: data.criteria ? data.criteria.map(this.transformCriteriaToDto) : existing.criteria.map(this.transformCriteriaToDto),
        approvers: [] // For now, we'll handle approval levels differently
      };

      const response = await apiClient.put<WorkflowDefinitionResponse>(
        `${this.BASE_URL}/${organizationId}/workflows/definitions/${id}`,
        requestData
      );

      const workflow = this.transformResponseToFrontend(response);

      return {
        data: workflow,
        message: 'Approval workflow updated successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error updating approval workflow with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an approval workflow
   */
  static async deleteApprovalWorkflow(
    id: string,
    organizationId: number = DEFAULT_ORGANIZATION_ID
  ): Promise<void> {
    try {
      await apiClient.delete<void>(
        `${this.BASE_URL}/${organizationId}/workflows/definitions/${id}`
      );
    } catch (error) {
      console.error(`Error deleting approval workflow with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all available form types for workflows
   */
  static async getFormTypes(organizationId: number = DEFAULT_ORGANIZATION_ID): Promise<FormTypeOption[]> {
    try {
      const responses = await apiClient.get<OrganizationFormsResponse[]>(
        `${this.BASE_URL}/${organizationId}/workflows/forms`
      );

      return responses.map(form => ({
        label: form.name,
        value: form.id.toString(),
        description: form.description || ''
      }));
    } catch (error) {
      console.error('Error fetching form types:', error);
      throw error;
    }
  }
}
