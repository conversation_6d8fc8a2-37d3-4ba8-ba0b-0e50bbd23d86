import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useParams } from '@tanstack/react-router';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import ApprovalWorkflowForm from '@/components/approvalWorkflow/ApprovalWorkflowForm';
import { ApprovalWorkflow, UpdateApprovalWorkflowRequest } from '@/types/approvalWorkflow.types';
import { useApprovalWorkflow, useUpdateApprovalWorkflow } from '@/hooks/useApprovalWorkflow';
import { approvalWorkflowsRoute } from '@/routes/private/approvalWorkflows.route';
import './EditApprovalWorkflow.css';

const EditApprovalWorkflow: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams({ strict: false });
  const toast = useRef<ToastRef>(null);

  // API hooks
  const { data: workflow, isLoading, error } = useApprovalWorkflow(id);
  const updateWorkflowMutation = useUpdateApprovalWorkflow();

  // Handle API errors
  useEffect(() => {
    if (error) {
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to load workflow',
        life: 3000
      });
    }
  }, [error]);

  // Handle form submission
  const handleSubmit = async (workflowData: UpdateApprovalWorkflowRequest) => {
    if (!id) return;

    try {
      await updateWorkflowMutation.mutateAsync({
        id,
        request: workflowData
      });

      // Show success toast and navigate back
      toast.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Approval workflow updated successfully',
        life: 3000
      });

      // Navigate back to workflows list after a short delay
      setTimeout(() => {
        navigate({ to: approvalWorkflowsRoute.to });
      }, 1500);
    } catch (error) {
      console.error('Error updating workflow:', error);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update approval workflow',
        life: 3000
      });
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: approvalWorkflowsRoute.to });
  };

  // Show error state if workflow not found
  if (error) {
    return (
      <div className="edit-approval-workflow p-4">
        <Toast ref={toast} position="top-right" />
        <Card
          title="Workflow Not Found"
          subtitle="The requested approval workflow could not be found"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto text-center"
        >
          <div className="py-4">
            <i className="pi pi-exclamation-triangle text-orange-500 text-4xl mb-3"></i>
            <p className="text-600 mb-4">
              The approval workflow you're looking for doesn't exist or may have been deleted.
            </p>
            <button
              className="p-button p-button-primary"
              onClick={handleCancel}
            >
              Back to Workflows
            </button>
          </div>
        </Card>
      </div>
    );
  }

  // Show loading state while fetching workflow
  if (isLoading || !workflow) {
    return (
      <div className="edit-approval-workflow p-4">
        <Toast ref={toast} position="top-right" />
        <Card
          title="Loading..."
          subtitle="Loading approval workflow details"
          variant="elevated"
          padding="large"
          className="max-w-5xl mx-auto"
        >
          <div className="flex justify-content-center py-4">
            <i className="pi pi-spin pi-spinner text-2xl text-primary"></i>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="edit-approval-workflow p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Edit Approval Workflow"
        subtitle={`Update workflow: ${workflow.name}`}
        variant="elevated"
        padding="large"
        className="max-w-5xl mx-auto"
      >
        <ApprovalWorkflowForm
          workflow={workflow}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={updateWorkflowMutation.isPending}
        />
      </Card>
    </div>
  );
};

export default EditApprovalWorkflow;
